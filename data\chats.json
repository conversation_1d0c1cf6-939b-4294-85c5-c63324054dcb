[{"id": "session-1750926678367", "title": "Testing JSON export", "messages": [{"id": "user-1750926678367", "text": "Testing JSON export", "sender": "user", "timestamp": "2025-06-26T08:31:18.367Z", "avatar": "", "chatId": "session-1750926678367"}, {"id": "ai-1750926678368", "text": "Okay! I can certainly generate an example JSON output for you.\n\nHere is a simple example:\n\n```json\n{\n  \"status\": \"success\",\n  \"message\": \"This is a sample JSON export test.\",\n  \"data\": {\n    \"user_id\": 12345,\n    \"username\": \"test_user\",\n    \"items\": [\n      {\"item_id\": 101, \"name\": \"Sample Item A\", \"quantity\": 5},\n      {\"item_id\": 102, \"name\": \"Sample Item B\", \"quantity\": 2},\n      {\"item_id\": 103, \"name\": \"Sample Item C\", \"quantity\": 10}\n    ],\n    \"settings\": {\n      \"notifications_enabled\": true,\n      \"theme\": \"dark\"\n    }\n  },\n  \"timestamp\": \"2023-10-27T10:30:00Z\"\n}\n```\n\nThis JSON object includes:\n*   `status`: A simple string indicating the result.\n*   `message`: A descriptive string.\n*   `data`: A nested object containing more detailed information.\n    *   `user_id`: A number.\n    *   `username`: A string.\n    *   `items`: An array of objects.\n    *   `settings`: Another nested object.\n*   `timestamp`: A string representing a date/time.\n\nLet me know if you'd like a different structure or specific type of data!", "sender": "ai", "timestamp": "2025-06-26T08:31:18.367Z", "isLoading": false, "chatId": "session-1750926678367", "isError": false, "groundingChunks": []}], "lastActivity": "2025-06-26T08:31:23.868Z", "iconName": "SpeechBubbleIcon"}, {"id": "chat1", "title": "AI Introduction", "iconName": "AiBotIcon", "messages": [], "lastActivity": "2024-07-30T10:00:00.000Z"}, {"id": "chat2", "title": "Russian Chat", "iconName": "SpeechBubbleIcon", "messages": [], "lastActivity": "2024-07-29T14:30:00.000Z"}, {"id": "chat3", "title": "Chatbot Design & Code", "messages": [], "lastActivity": "2024-07-27T09:15:00.000Z"}, {"id": "chat4", "title": "Prompt Engineering Course", "iconName": "FolderIcon", "messages": [], "lastActivity": "2024-07-22T11:00:00.000Z"}, {"id": "chat5", "title": "Data Analysis Qs", "messages": [], "lastActivity": "2024-07-15T16:45:00.000Z"}, {"id": "chat6", "title": "Old Project Ideas", "iconName": "CubeIcon", "messages": [], "lastActivity": "2024-06-20T10:00:00.000Z"}, {"id": "chat7", "title": "Archived Notes", "messages": [], "lastActivity": "2024-05-20T10:00:00.000Z"}, {"id": "chat8", "title": "Really Old Stuff", "iconName": "AiBotIcon", "messages": [], "lastActivity": "2023-03-15T10:00:00.000Z"}, {"id": "chat9", "title": "Ancient History", "iconName": "SpeechBubbleIcon", "messages": [], "lastActivity": "2023-07-30T10:00:00.000Z"}]