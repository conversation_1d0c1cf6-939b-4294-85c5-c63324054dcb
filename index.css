@tailwind base;
@tailwind components;
@tailwind utilities;

html, body, #root {
  height: 100%;
  min-height: 100%;
}

/* Custom scrollbars for Sidebar, MessageDisplay, SettingsPage, and AccountModal */
.sidebar-scrollbar::-webkit-scrollbar,
.message-scrollbar::-webkit-scrollbar,
.settings-scrollbar::-webkit-scrollbar,
.account-scrollbar::-webkit-scrollbar {
  width: 10px;
  background: #f8fafc;
}
.sidebar-scrollbar::-webkit-scrollbar-thumb,
.message-scrollbar::-webkit-scrollbar-thumb,
.settings-scrollbar::-webkit-scrollbar-thumb,
.account-scrollbar::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 8px;
}
.sidebar-scrollbar::-webkit-scrollbar-track,
.message-scrollbar::-webkit-scrollbar-track,
.settings-scrollbar::-webkit-scrollbar-track,
.account-scrollbar::-webkit-scrollbar-track {
  background: #f8fafc;
}

/* Dark theme scrollbars using Tail<PERSON>'s .dark class */
.dark .sidebar-scrollbar::-webkit-scrollbar,
.dark .message-scrollbar::-webkit-scrollbar,
.dark .settings-scrollbar::-webkit-scrollbar,
.dark .account-scrollbar::-webkit-scrollbar {
  background: #0f172a;
}
.dark .sidebar-scrollbar::-webkit-scrollbar-thumb,
.dark .message-scrollbar::-webkit-scrollbar-thumb,
.dark .settings-scrollbar::-webkit-scrollbar-thumb,
.dark .account-scrollbar::-webkit-scrollbar-thumb {
  background: #334155;
  border-radius: 8px;
}
.dark .sidebar-scrollbar::-webkit-scrollbar-track,
.dark .message-scrollbar::-webkit-scrollbar-track,
.dark .settings-scrollbar::-webkit-scrollbar-track,
.dark .account-scrollbar::-webkit-scrollbar-track {
  background: #0f172a;
}

/* For Firefox */
.sidebar-scrollbar,
.message-scrollbar,
.settings-scrollbar,
.account-scrollbar {
  scrollbar-color: #e2e8f0 #f8fafc;
  scrollbar-width: thin;
}
.dark .sidebar-scrollbar,
.dark .message-scrollbar,
.dark .settings-scrollbar,
.dark .account-scrollbar {
  scrollbar-color: #334155 #0f172a;
}

