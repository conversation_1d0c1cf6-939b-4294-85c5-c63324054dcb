import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  CubeIcon,
  GlobeAltIcon,
  ArrowUpIcon,
  SUGGESTION_CHIPS,
} from "../constants";

interface ChatInputProps {
  onSendMessage: (message: string, useWebSearch: boolean) => void;
  isLoading: boolean;
  showSuggestions?: boolean;
}

const SuggestionChip: React.FC<{
  text: string;
  icon: React.ReactNode;
  onClick: () => void;
}> = ({ text, icon, onClick }) => (
  <button
    onClick={onClick}
    className="flex items-center bg-white dark:bg-slate-700 hover:bg-slate-100 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 px-3 py-1.5 border border-slate-200 dark:border-slate-600 rounded-lg text-sm shadow-sm transition-colors"
    data-oid="umnz3:0"
  >
    {icon && React.isValidElement(icon)
      ? React.cloneElement(icon as React.ReactElement<{ className?: string }>, {
          className: "w-4 h-4 mr-1.5 text-slate-500 dark:text-slate-400",
        })
      : icon}
    {text}
  </button>
);

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  isLoading,
  showSuggestions = false,
}) => {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState("");
  const [useArtifacts, setUseArtifacts] = useState(false);
  const [useWebSearch, setUseWebSearch] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(event.target.value);
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [inputValue]);

  const handleSend = () => {
    if (inputValue.trim() && !isLoading) {
      onSendMessage(inputValue.trim(), useWebSearch);
      setInputValue("");
      setUseWebSearch(false); // Optionally reset web search
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  const handleSuggestionClick = (text: string) => {
    setInputValue(text);
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto" data-oid="sf3kk:n">
      <div
        className="bg-white dark:bg-slate-700/80 rounded-xl shadow-lg p-3 border border-slate-200 dark:border-slate-600/50"
        data-oid="cyh21uv"
      >
        <textarea
          ref={textareaRef}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={t("chatInputPlaceholder")}
          className="w-full p-3 text-slate-700 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-400/70 resize-none focus:outline-none text-base bg-transparent"
          rows={1}
          style={{ maxHeight: "200px", overflowY: "auto" }}
          disabled={isLoading}
          data-oid="8g5l1:3"
        />

        <div
          className="flex items-center justify-between mt-2 pt-2 border-t border-slate-100 dark:border-slate-600/50"
          data-oid="t4uhzm-"
        >
          <div className="flex items-center space-x-2" data-oid="10y1b3a">
            <button
              onClick={() => setUseArtifacts(!useArtifacts)}
              className={`flex items-center px-3 py-1.5 rounded-md text-sm transition-colors 
                ${
                  useArtifacts
                    ? "bg-blue-100 dark:bg-blue-600 text-blue-700 dark:text-blue-100"
                    : "bg-slate-100 dark:bg-slate-600 hover:bg-slate-200 dark:hover:bg-slate-500 text-slate-600 dark:text-slate-300"
                }`}
              data-oid="cx.c6-k"
            >
              <CubeIcon className="w-4 h-4 mr-1.5" data-oid="akzdyb6" />
              {t("artifacts")}
            </button>
            <button
              onClick={() => setUseWebSearch(!useWebSearch)}
              className={`flex items-center px-3 py-1.5 rounded-md text-sm transition-colors 
                ${
                  useWebSearch
                    ? "bg-blue-100 dark:bg-blue-600 text-blue-700 dark:text-blue-100"
                    : "bg-slate-100 dark:bg-slate-600 hover:bg-slate-200 dark:hover:bg-slate-500 text-slate-600 dark:text-slate-300"
                }`}
              data-oid="vmlq5p6"
            >
              <GlobeAltIcon className="w-4 h-4 mr-1.5" data-oid="d0du7-y" />
              {t("webSearch")}
            </button>
          </div>
          <button
            onClick={handleSend}
            disabled={isLoading || !inputValue.trim()}
            className="p-2.5 bg-slate-700 dark:bg-blue-600 hover:bg-slate-800 dark:hover:bg-blue-700 text-white rounded-lg disabled:bg-slate-300 dark:disabled:bg-slate-500 transition-colors"
            aria-label="Send message"
            data-oid="o94ogtw"
          >
            {isLoading ? (
              <svg
                className="animate-spin h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                data-oid="8v.mzc4"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                  data-oid="mii9ln:"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  data-oid="37c7:gf"
                ></path>
              </svg>
            ) : (
              <ArrowUpIcon
                className="w-5 h-5 transform rotate-0"
                data-oid="xnte9._"
              />
            )}
          </button>
        </div>
      </div>
      {showSuggestions && (
        <div
          className="flex flex-wrap items-center justify-center gap-2 mt-4"
          data-oid="y5z7af:"
        >
          {SUGGESTION_CHIPS.map((chip) => (
            <SuggestionChip
              key={chip.text}
              text={chip.text}
              icon={chip.icon}
              onClick={() => handleSuggestionClick(chip.text)}
              data-oid="aw-5ln4"
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ChatInput;
