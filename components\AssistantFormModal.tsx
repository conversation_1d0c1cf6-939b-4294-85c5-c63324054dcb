import React, { useState, useEffect } from "react";
import { Assistant, addAssistant, updateAssistant } from "../types/assistant";

interface AssistantFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: Assistant | null;
}

const defaultParams = [
  { key: "", type: "string", value: "" },
];

const AssistantFormModal: React.FC<AssistantFormModalProps> = ({ isOpen, onClose, initialData }) => {
  const [emoji, setEmoji] = useState("");
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [instructions, setInstructions] = useState("");
  const [parameters, setParameters] = useState<any[]>(defaultParams);
  const [error, setError] = useState("");

  useEffect(() => {
    if (initialData) {
      setEmoji(initialData.emoji || "");
      setName(initialData.name || "");
      setDescription(initialData.description || "");
      setInstructions(initialData.instructions || "");
      setParameters(
        initialData.parameters
          ? Object.entries(initialData.parameters).map(([key, value]) => ({ key, type: typeof value, value }))
          : defaultParams
      );
    } else {
      setEmoji("");
      setName("");
      setDescription("");
      setInstructions("");
      setParameters(defaultParams);
    }
    setError("");
  }, [initialData, isOpen]);

  const handleParamChange = (idx: number, field: string, value: string) => {
    setParameters(params =>
      params.map((p, i) => (i === idx ? { ...p, [field]: value } : p))
    );
  };

  const handleAddParam = () => {
    setParameters(params => [...params, { key: "", type: "string", value: "" }]);
  };

  const handleRemoveParam = (idx: number) => {
    setParameters(params => params.filter((_, i) => i !== idx));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!name) {
      setError("Name is required.");
      return;
    }
    const paramObj: Record<string, any> = {};
    parameters.forEach(p => {
      if (p.key) paramObj[p.key] = p.value;
    });
    const assistant: Assistant = {
      id: initialData?.id || Date.now().toString(),
      emoji,
      name,
      description,
      instructions,
      parameters: paramObj,
    };
    if (initialData) {
      updateAssistant(assistant);
    } else {
      addAssistant(assistant);
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
      <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl w-full max-w-lg p-8 relative">
        <button className="absolute top-4 right-4 text-slate-400 hover:text-slate-700 dark:hover:text-white" onClick={onClose} aria-label="Close">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <h2 className="text-xl font-bold mb-6 text-slate-800 dark:text-white">{initialData ? "Edit Assistant" : "Add Assistant"}</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex gap-2 items-center">
            <input
              className="w-12 h-12 text-2xl text-center rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700"
              value={emoji}
              onChange={e => setEmoji(e.target.value)}
              maxLength={2}
              placeholder="😊"
              aria-label="Emoji"
            />
            <input
              className="flex-1 px-3 py-2 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Name"
              required
            />
          </div>
          <textarea
            className="w-full px-3 py-2 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
            value={description}
            onChange={e => setDescription(e.target.value)}
            placeholder="Description (optional)"
            rows={2}
          />
          <textarea
            className="w-full px-3 py-2 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
            value={instructions}
            onChange={e => setInstructions(e.target.value)}
            placeholder="Instructions"
            rows={3}
          />
          <div>
            <div className="font-medium mb-2 text-slate-700 dark:text-slate-200">Parameters</div>
            <div className="space-y-2">
              {parameters.map((p, idx) => (
                <div key={idx} className="flex gap-2 items-center">
                  <input
                    className="w-24 px-2 py-1 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-sm text-slate-800 dark:text-white"
                    value={p.key}
                    onChange={e => handleParamChange(idx, "key", e.target.value)}
                    placeholder="Key"
                  />
                  <select
                    className="px-2 py-1 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-slate-800 text-sm dark:text-white"
                    value={p.type}
                    onChange={e => handleParamChange(idx, "type", e.target.value)}
                  >
                    <option value="string">String</option>
                    <option value="number">Number</option>
                    <option value="boolean">Boolean</option>
                  </select>
                  <input
                    className="flex-1 px-2 py-1 rounded border border-slate-300 dark:border-slate-600 bg-slate-50 dark:bg-slate-700 text-slate-800 text-sm dark:text-white"
                    value={p.value}
                    onChange={e => handleParamChange(idx, "value", e.target.value)}
                    placeholder="Value"
                  />
                  <button type="button" className="text-red-500 hover:text-red-700 ml-1" onClick={() => handleRemoveParam(idx)} title="Remove">🗑️</button>
                </div>
              ))}
            </div>
            <button type="button" className="mt-2 px-3 py-1 rounded bg-slate-200 dark:bg-slate-700 text-sm text-slate-700 dark:text-slate-200 hover:bg-slate-300 dark:hover:bg-slate-600" onClick={handleAddParam}>
              + Add Parameter
            </button>
          </div>
          {error && <div className="text-red-500 text-sm">{error}</div>}
          <div className="flex justify-end gap-2 mt-4">
            <button type="button" className="px-4 py-2 rounded bg-slate-200 dark:bg-slate-700 text-sm text-slate-700 dark:text-slate-200 hover:bg-slate-300 dark:hover:bg-slate-600" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="px-4 py-2 rounded bg-blue-600 text-sm text-white hover:bg-blue-700">
              {initialData ? "Save" : "Add"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssistantFormModal;
