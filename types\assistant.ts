// types/assistant.ts

export interface Assistant {
  id: string;
  emoji: string;
  name: string;
  description: string;
  instructions: string;
  parameters?: Record<string, any>;
}

export const ASSISTANTS_STORAGE_KEY = "assistants";

export function loadAssistants(): Assistant[] {
  try {
    const data = localStorage.getItem(ASSISTANTS_STORAGE_KEY);
    if (!data) return [];
    return JSON.parse(data);
  } catch {
    return [];
  }
}

export function saveAssistants(assistants: Assistant[]) {
  localStorage.setItem(ASSISTANTS_STORAGE_KEY, JSON.stringify(assistants));
}

export function addAssistant(assistant: Assistant) {
  const assistants = loadAssistants();
  assistants.push(assistant);
  saveAssistants(assistants);
}

export function updateAssistant(updated: Assistant) {
  const assistants = loadAssistants().map(a => a.id === updated.id ? updated : a);
  saveAssistants(assistants);
}

export function deleteAssistant(id: string) {
  const assistants = loadAssistants().filter(a => a.id !== id);
  saveAssistants(assistants);
}
