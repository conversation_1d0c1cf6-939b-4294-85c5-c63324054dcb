import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  ICON_COMPONENTS,
  ZLogoIcon,
  SidebarToggleIcon,
  PencilIcon,
  SearchIcon,
  ChevronDownIcon,
} from "../constants";
import { ChatSession, User } from "../types";
import UserProfileMenu from "./UserProfileMenu";
import { View } from "../App";

interface SidebarProps {
  chatSessions: ChatSession[];
  onNewChat: () => void;
  onSelectChat: (chatId: string) => void;
  activeChatId: string | null;
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
  isSmallScreen: boolean;
  navigateTo: (view: View) => void;
  currentView: View;
  isAuthenticated: boolean;
  currentUser: User;
  handleLogout: () => void;
  openSignInModal: () => void;
}

interface SidebarHeaderProps {
  onToggle: () => void;
  isSidebarOpen: boolean;
}

const isSameDay = (date1: Date, date2: Date): boolean =>
  date1.getFullYear() === date2.getFullYear() &&
  date1.getMonth() === date2.getMonth() &&
  date1.getDate() === date2.getDate();

const isYesterday = (date: Date, today: Date): boolean => {
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  return isSameDay(date, yesterday);
};

const getStartOfWeek = (date: Date): Date => {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1);
  return new Date(d.setDate(diff));
};

const isThisWeek = (date: Date, today: Date): boolean => {
  const startOfThisWeek = getStartOfWeek(today);
  return (
    date >= startOfThisWeek &&
    date <= today &&
    !isSameDay(date, today) &&
    !isYesterday(date, today)
  );
};

const isLastWeek = (date: Date, today: Date): boolean => {
  const startOfThisWeek = getStartOfWeek(today);
  const startOfLastWeek = new Date(startOfThisWeek);
  startOfLastWeek.setDate(startOfThisWeek.getDate() - 7);
  const endOfLastWeek = new Date(startOfThisWeek);
  endOfLastWeek.setDate(startOfThisWeek.getDate() - 1);
  endOfLastWeek.setHours(23, 59, 59, 999);
  return date >= startOfLastWeek && date <= endOfLastWeek;
};

const isThisMonth = (date: Date, today: Date): boolean => {
  return (
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    !isSameDay(date, today) &&
    !isYesterday(date, today) &&
    !isThisWeek(date, today) &&
    !isLastWeek(date, today)
  );
};

const isLastMonth = (date: Date, today: Date): boolean => {
  const lastMonthDate = new Date(today);
  lastMonthDate.setMonth(today.getMonth() - 1);
  return (
    date.getFullYear() === lastMonthDate.getFullYear() &&
    date.getMonth() === lastMonthDate.getMonth()
  );
};

const getRelativeDateGroup = (date: Date): string => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const normalizedDate =
    date instanceof Date ? new Date(date) : new Date(String(date));
  if (isNaN(normalizedDate.getTime())) {
    console.warn(`Invalid date provided to getRelativeDateGroup:`, date);
    return "INVALID DATE";
  }
  normalizedDate.setHours(0, 0, 0, 0);

  if (isSameDay(normalizedDate, today)) return "TODAY";
  if (isYesterday(normalizedDate, today)) return "YESTERDAY";
  if (isThisWeek(normalizedDate, today)) return "THIS WEEK";
  if (isLastWeek(normalizedDate, today)) return "LAST WEEK";
  if (isThisMonth(normalizedDate, today)) return "THIS MONTH";
  if (isLastMonth(normalizedDate, today)) return "LAST MONTH";

  const monthName = normalizedDate.toLocaleString("default", { month: "long" });
  const year = normalizedDate.getFullYear();
  return `${monthName.toUpperCase()} ${year}`;
};

const SidebarHeader: React.FC<SidebarHeaderProps> = ({
  onToggle,
  isSidebarOpen,
}) => (
  <div
    className="flex items-center justify-between p-3 border-b border-slate-200 dark:border-slate-700 h-16 flex-shrink-0"
    data-oid="ndjayan"
  >
    <ZLogoIcon
      className="w-7 h-7 text-black dark:text-white"
      data-oid="95xdkj."
    />

    <button
      onClick={onToggle}
      className="p-1 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200"
      aria-label={isSidebarOpen ? "Collapse sidebar" : "Expand sidebar"}
      aria-expanded={isSidebarOpen}
      data-oid="gs2bng3"
    >
      <SidebarToggleIcon
        className={`w-6 h-6 transition-transform duration-300 transform ${isSidebarOpen ? "rotate-0" : "rotate-180"}`}
        data-oid="86-q_g5"
      />
    </button>
  </div>
);

const NewChatButton: React.FC<{
  onClick: () => void;
  isSidebarOpen: boolean;
}> = ({ onClick, isSidebarOpen }) => {
  const { t } = useTranslation();
  return (
    <button
      onClick={onClick}
      className={`flex items-center w-full px-3 py-2.5 my-4 text-sm font-medium text-slate-700 dark:text-slate-200 bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 rounded-lg transition-colors ${!isSidebarOpen && "justify-center"}`}
      title={!isSidebarOpen ? t("newChat") : undefined}
      data-oid="svtyzhi"
    >
      <PencilIcon
        className={`w-5 h-5 ${isSidebarOpen ? "mr-2" : "mr-0"}`}
        data-oid="0gf275c"
      />

      {isSidebarOpen && t("newChat")}
    </button>
  );
};

const SearchBar: React.FC<{ isSidebarOpen: boolean }> = ({ isSidebarOpen }) => {
  const { t } = useTranslation();
  return (
    <div
      className={`relative px-2 mb-4 transition-opacity duration-150 ${isSidebarOpen ? "opacity-100" : "opacity-0 invisible"}`}
      data-oid="vfuh5u9"
    >
      <div
        className="absolute inset-y-0 left-0 flex items-center pl-5 pointer-events-none"
        data-oid="vi4:0l8"
      >
        <SearchIcon
          className="w-4 h-4 text-slate-400 dark:text-slate-500"
          data-oid="ny1h26_"
        />
      </div>
      <input
        type="search"
        placeholder={t("search")}
        className="w-full py-2 pl-10 pr-4 text-sm text-slate-700 dark:text-slate-200 bg-slate-100 dark:bg-slate-700 border border-transparent rounded-lg focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400 dark:placeholder-slate-500"
        tabIndex={isSidebarOpen ? 0 : -1}
        data-oid="9iqyeu4"
      />
    </div>
  );
};

interface ChatListItemProps {
  chat: ChatSession & { iconName?: string };
  isActive: boolean;
  onSelect: () => void;
  isSidebarOpen: boolean;
}

const ChatListItem: React.FC<ChatListItemProps> = ({
  chat,
  isActive,
  onSelect,
  isSidebarOpen,
}) => {
  const IconComponent = chat.iconName
    ? ICON_COMPONENTS[chat.iconName] || ICON_COMPONENTS.DefaultChatIcon
    : ICON_COMPONENTS.DefaultChatIcon;

  return (
    <button
      onClick={onSelect}
      className={`flex items-center w-full px-3 py-2.5 text-sm text-left rounded-md hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors 
        ${
          isActive
            ? "bg-slate-100 dark:bg-slate-700 font-medium text-slate-800 dark:text-slate-100"
            : "text-slate-600 dark:text-slate-300"
        } 
        ${!isSidebarOpen && "justify-center"}`}
      title={!isSidebarOpen ? chat.title : undefined}
      tabIndex={isSidebarOpen ? 0 : -1}
      data-oid="h0qkgum"
    >
      <span
        className={`${isSidebarOpen ? "mr-2.5" : "mr-0"}`}
        data-oid="qgkupn."
      >
        <IconComponent className="w-5 h-5" data-oid="3dw8peg" />{" "}
        {/* Icons with internal colors might need dark mode variants */}
      </span>
      {isSidebarOpen && (
        <span className="truncate" data-oid="v6gr5qm">
          {chat.title}
        </span>
      )}
    </button>
  );
};

interface UserProfileSidebarProps {
  isSidebarOpen: boolean;
  onClick: () => void;
  userMenuButtonId: string;
  isUserMenuOpen: boolean;
  isAuthenticated: boolean;
  currentUser: User;
}

const UserProfileSidebar = React.forwardRef<
  HTMLDivElement,
  UserProfileSidebarProps
>(
  (
    {
      isSidebarOpen,
      onClick,
      userMenuButtonId,
      isUserMenuOpen,
      isAuthenticated,
      currentUser,
    },
    ref,
  ) => {
    if (!isAuthenticated || !currentUser || !currentUser.name) return null;

    return (
      <div
        ref={ref}
        onClick={onClick}
        className={`block w-full border-t border-slate-200 dark:border-slate-700 mt-auto flex-shrink-0 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-all duration-150 min-h-[60px] relative z-10 ${isSidebarOpen ? "opacity-100 p-4" : "opacity-0 invisible py-3 px-2"} `}
        role="button"
        aria-haspopup="true"
        aria-expanded={isUserMenuOpen}
        id={userMenuButtonId}
        tabIndex={isSidebarOpen ? 0 : -1}
        data-oid="oqe5nbr"
      >
        <div
          className={`flex items-center ${isSidebarOpen ? "space-x-3" : "justify-center"}`}
          data-oid="wk705ug"
        >
          {currentUser.avatarUrl ? (
            <img
              src={currentUser.avatarUrl}
              alt={currentUser.name}
              className="w-8 h-8 rounded-full"
              data-oid="1qix8r1"
            />
          ) : (
            <div
              className="w-8 h-8 rounded-full bg-slate-300 dark:bg-slate-600 flex items-center justify-center text-slate-500 dark:text-slate-300 text-sm font-semibold"
              data-oid="fexva:j"
            >
              {currentUser.name.substring(0, 1).toUpperCase()}
            </div>
          )}
          {isSidebarOpen && (
            <div className="flex-1 min-w-0" data-oid="iixk_j7">
              <p
                className="text-sm font-medium text-slate-800 dark:text-slate-100 truncate"
                data-oid="5l7.puu"
              >
                {currentUser.name.split("(")[0].trim()}
              </p>
              <p
                className="text-xs text-slate-500 dark:text-slate-400 truncate"
                data-oid="ajvocyc"
              >
                {currentUser.name}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  },
);

const Sidebar: React.FC<SidebarProps> = ({
  chatSessions,
  onNewChat,
  onSelectChat,
  activeChatId,
  isSidebarOpen,
  onToggleSidebar,
  isSmallScreen,
  navigateTo,
  currentView,
  isAuthenticated,
  currentUser,
  handleLogout,
  openSignInModal,
}) => {
  const { t } = useTranslation();
  const [isAllChatsOpen, setIsAllChatsOpen] = useState(true);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const userProfileRef = useRef<HTMLDivElement>(null);
  const userMenuButtonId = "sidebar-user-menu-button";

  const toggleAllChats = () => {
    setIsAllChatsOpen((prev) => !prev);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen((prev) => !prev);
  };

  const groupedChats = chatSessions.reduce(
    (acc, chat) => {
      const chatDate =
        chat.lastActivity instanceof Date
          ? chat.lastActivity
          : new Date(String(chat.lastActivity));
      if (isNaN(chatDate.getTime())) {
        console.warn(`Invalid date for chat: ${chat.title}`, chat.lastActivity);
        const group = "UNKNOWN DATE";
        if (!acc[group]) acc[group] = [];
        acc[group].push(chat as ChatSession & { iconName?: string });
        return acc;
      }
      const group = getRelativeDateGroup(chatDate);
      if (!acc[group]) acc[group] = [];
      acc[group].push(chat as ChatSession & { iconName?: string });
      acc[group].sort((a, b) => {
        const dateA =
          a.lastActivity instanceof Date
            ? a.lastActivity
            : new Date(String(a.lastActivity));
        const dateB =
          b.lastActivity instanceof Date
            ? b.lastActivity
            : new Date(String(b.lastActivity));
        return dateB.getTime() - dateA.getTime();
      });
      return acc;
    },
    {} as Record<string, (ChatSession & { iconName?: string })[]>,
  );

  const predefinedGroupOrder = [
    "TODAY",
    "YESTERDAY",
    "THIS WEEK",
    "LAST WEEK",
    "THIS MONTH",
    "LAST MONTH",
  ];

  const sortedGroupKeys = Object.keys(groupedChats).sort((a, b) => {
    const aIndex = predefinedGroupOrder.indexOf(a);
    const bIndex = predefinedGroupOrder.indexOf(b);
    if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
    if (aIndex !== -1) return -1;
    if (bIndex !== -1) return 1;
    const mostRecentDateA = groupedChats[a][0]?.lastActivity;
    const mostRecentDateB = groupedChats[b][0]?.lastActivity;
    const dateA =
      mostRecentDateA instanceof Date
        ? mostRecentDateA
        : new Date(String(mostRecentDateA));
    const dateB =
      mostRecentDateB instanceof Date
        ? mostRecentDateB
        : new Date(String(mostRecentDateB));
    if (dateA && dateB && !isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
      return dateB.getTime() - dateA.getTime();
    }
    if (a.startsWith("UNKNOWN") || a.startsWith("INVALID")) return 1;
    if (b.startsWith("UNKNOWN") || b.startsWith("INVALID")) return -1;
    return a.localeCompare(b);
  });

  const baseClasses =
    "h-full bg-white dark:bg-slate-800 text-slate-800 dark:text-slate-200 flex flex-col transition-all duration-300 ease-in-out overflow-hidden";
  let dynamicClasses = "";

  if (isSmallScreen) {
    dynamicClasses = `fixed top-0 left-0 z-50 shadow-xl border-r border-slate-200 dark:border-slate-700 w-72 ${isSidebarOpen && currentView === "chat" && isAuthenticated ? "translate-x-0" : "-translate-x-full"}`;
  } else {
    dynamicClasses = `border-r border-slate-200 dark:border-slate-700 ${isSidebarOpen && isAuthenticated ? "w-72 opacity-100 p-0" : "w-0 opacity-0 invisible p-0 border-0 dark:border-0"}`;
  }

  const ariaHidden =
    (!isSidebarOpen && !isSmallScreen && isAuthenticated) ||
    (isSmallScreen && !isSidebarOpen && isAuthenticated) ||
    !isAuthenticated;

  return (
    <>
      <div
        className={`${baseClasses} ${dynamicClasses}`}
        aria-hidden={ariaHidden}
        data-oid="9:8ogn8"
      >
        {isAuthenticated && (
          <>
            <SidebarHeader
              onToggle={onToggleSidebar}
              isSidebarOpen={isSidebarOpen}
              data-oid="38:pxzx"
            />

            <div
              className={`flex-grow flex flex-col min-h-0 ${isSidebarOpen || (isSmallScreen && isSidebarOpen) ? "opacity-100" : "opacity-0 invisible"}`}
              data-oid="ywgg4du"
            >
              <div className={`flex-shrink-0 px-2`} data-oid="r:2j1fu">
                <NewChatButton
                  onClick={onNewChat}
                  isSidebarOpen={isSidebarOpen}
                  data-oid="bfkbj6d"
                />
              </div>
              <SearchBar isSidebarOpen={isSidebarOpen} data-oid="0wgoxe7" />
              <nav
                className={`flex-1 px-2 space-y-1 overflow-y-auto overflow-x-hidden transition-opacity duration-150 ${isSidebarOpen ? "opacity-100" : "opacity-0 invisible"}`}
                data-oid="7uyoy-s"
              >
                <div
                  className={`flex items-center justify-between px-3 py-2 text-sm font-medium text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md cursor-pointer transition-opacity duration-150 ${isSidebarOpen ? "opacity-100" : "opacity-0 invisible"}`}
                  onClick={toggleAllChats}
                  role="button"
                  tabIndex={isSidebarOpen ? 0 : -1}
                  aria-expanded={isAllChatsOpen}
                  aria-controls="all-chats-content"
                  data-oid="2-os4s-"
                >
                  <span data-oid="v3kroa_">{t("allChats")}</span>
                  <ChevronDownIcon
                    className={`w-4 h-4 text-slate-500 dark:text-slate-400 transform transition-transform duration-300 ${isAllChatsOpen ? "rotate-180" : "rotate-0"}`}
                    data-oid="-j43oyx"
                  />
                </div>
                <div
                  id="all-chats-content"
                  className={`transition-all duration-300 ease-in-out overflow-hidden ${isAllChatsOpen && isSidebarOpen ? "max-h-screen opacity-100" : "max-h-0 opacity-0"}`}
                  data-oid="5:4bt24"
                >
                  {sortedGroupKeys.map(
                    (groupName) =>
                      groupedChats[groupName] &&
                      groupedChats[groupName].length > 0 && (
                        <div
                          key={groupName}
                          className={`mt-2 transition-opacity duration-150 ${isSidebarOpen ? "opacity-100" : "opacity-0 invisible"}`}
                          data-oid="pe1_s6_"
                        >
                          <h3
                            className="px-3 py-1 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider"
                            data-oid="ql.p..b"
                          >
                            {groupName}
                          </h3>
                          {groupedChats[groupName].map((chat) => (
                            <ChatListItem
                              key={chat.id}
                              chat={chat}
                              isActive={activeChatId === chat.id}
                              onSelect={() => onSelectChat(chat.id)}
                              isSidebarOpen={isSidebarOpen}
                              data-oid="afg3gfj"
                            />
                          ))}
                        </div>
                      ),
                  )}
                </div>
              </nav>
              <UserProfileSidebar
                ref={userProfileRef}
                isSidebarOpen={isSidebarOpen}
                onClick={toggleUserMenu}
                userMenuButtonId={userMenuButtonId}
                isUserMenuOpen={isUserMenuOpen}
                isAuthenticated={isAuthenticated}
                currentUser={currentUser}
                data-oid="rb5zf8s"
              />
            </div>
          </>
        )}
      </div>
      {isAuthenticated && isUserMenuOpen && (
        <UserProfileMenu
          isOpen={isUserMenuOpen}
          onClose={() => setIsUserMenuOpen(false)}
          triggerRef={userProfileRef}
          menuPosition="top-left"
          triggerButtonId={userMenuButtonId}
          navigateTo={navigateTo}
          handleLogout={handleLogout}
          data-oid=":00bhle"
        />
      )}
    </>
  );
};

export default Sidebar;
