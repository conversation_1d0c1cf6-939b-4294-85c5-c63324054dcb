{"name": "gemini-ai-chat-interface", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google/genai": "^1.6.0", "@tailwindcss/typography": "^0.5.16", "i18next": "^23.12.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i": "^0.2.1", "react-i18next": "^15.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}