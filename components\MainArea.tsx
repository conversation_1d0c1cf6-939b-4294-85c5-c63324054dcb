import React, { useState, useCallback, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Message, Model, User } from "../types";
import {
  MODELS,
  ChevronDownIcon,
  GithubIcon,
  TwitterXIcon,
  DiscordIcon,
  BrainIcon,
  GEMINI_MODEL_NAME,
  SidebarToggleIcon,
  PencilIcon,
  ZLogoIcon,
} from "../constants";
import ChatInput from "./ChatInput";
import MessageDisplay from "./MessageDisplay";
import UserProfileMenu from "./UserProfileMenu";
import {
  sendMessageToGeminiStream,
  resetChatSession,
} from "../services/geminiService";
import { View } from "../App";

interface MainHeaderProps {
  selectedModel: Model;
  onModelChange: (modelId: string) => void;
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
  onNewChat: () => void;
  navigateTo: (view: View) => void;
  isAuthenticated: boolean;
  currentUser: User;
  handleLogout: () => void;
  openSignInModal: () => void;
}

const ModelDropdown: React.FC<{
  selectedModel: Model;
  onModelChange: (modelId: string) => void;
}> = ({ selectedModel, onModelChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef} data-oid="xl03eua">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center text-slate-800 dark:text-slate-200 hover:text-slate-900 dark:hover:text-slate-100 focus:outline-none"
        id="model-dropdown-button"
        aria-haspopup="true"
        aria-expanded={isOpen}
        data-oid="bqktk08"
      >
        <span className="text-lg font-semibold" data-oid="_rp:7gk">
          {selectedModel.name}
        </span>
        <ChevronDownIcon
          className={`w-5 h-5 ml-1 transition-transform transform ${isOpen ? "rotate-180" : "rotate-0"}`}
          data-oid="5a.._bx"
        />
      </button>
      {isOpen && (
        <div
          className="absolute left-0 mt-2 w-56 bg-white dark:bg-slate-700 rounded-md shadow-lg ring-1 ring-black dark:ring-slate-600 ring-opacity-5 z-20 py-1"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="model-dropdown-button"
          data-oid="ws8fcjl"
        >
          {MODELS.map((model) => (
            <button
              key={model.id}
              onClick={() => {
                onModelChange(model.id);
                setIsOpen(false);
              }}
              className="block w-full text-left px-4 py-2 text-sm text-slate-700 dark:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-600"
              role="menuitem"
              data-oid="r--yrn."
            >
              {model.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

const MainHeader: React.FC<MainHeaderProps> = ({
  selectedModel,
  onModelChange,
  isSidebarOpen,
  onToggleSidebar,
  onNewChat,
  navigateTo,
  isAuthenticated,
  currentUser,
  handleLogout,
  openSignInModal,
}) => {
  const { t } = useTranslation();
  const [isHeaderUserMenuOpen, setIsHeaderUserMenuOpen] = useState(false);
  const headerAvatarRef = useRef<HTMLButtonElement>(null);
  const headerUserMenuButtonId = "header-user-menu-button";

  const toggleHeaderUserMenu = () => {
    setIsHeaderUserMenuOpen((prev) => !prev);
  };

  return (
    <>
      <div
        className="flex items-center justify-between px-3 sm:px-5 h-16 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 flex-shrink-0"
        data-oid="pimono0"
      >
        <div className="flex items-center space-x-3" data-oid="aj-ar7f">
          {(!isSidebarOpen || !isAuthenticated) && (
            <div className="flex items-center space-x-1" data-oid="yk4tv5:">
              <button
                onClick={onToggleSidebar}
                className="p-2 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md"
                aria-label={
                  isAuthenticated
                    ? isSidebarOpen
                      ? "Collapse sidebar"
                      : "Expand sidebar"
                    : t("chatHistoryAccessTitle")
                }
                data-oid="g3i7f-6"
              >
                <SidebarToggleIcon
                  className={`w-6 h-6 transition-transform duration-300 transform ${isSidebarOpen && isAuthenticated ? "rotate-0" : "rotate-180"}`}
                  data-oid="u0qs1jf"
                />
              </button>
              <button
                onClick={onNewChat}
                className="p-2 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md"
                aria-label={t("newChat")}
                data-oid="ca44qyp"
              >
                <PencilIcon className="w-5 h-5" data-oid="q_n3lbl" />
              </button>
            </div>
          )}
          <div data-oid="3v8drig">
            <ModelDropdown
              selectedModel={selectedModel}
              onModelChange={onModelChange}
              data-oid="3plvaso"
            />

            <p
              className="text-xs text-slate-500 dark:text-slate-400 mt-0.5"
              data-oid="liu9lch"
            >
              {t("setAsDefault")}
            </p>
          </div>
        </div>
        {isAuthenticated && currentUser && currentUser.name ? (
          <button
            ref={headerAvatarRef}
            onClick={toggleHeaderUserMenu}
            className="rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-800"
            id={headerUserMenuButtonId}
            aria-haspopup="true"
            aria-expanded={isHeaderUserMenuOpen}
            aria-label="User menu"
            data-oid="9fm1l7j"
          >
            {currentUser.avatarUrl ? (
              <img
                src={currentUser.avatarUrl}
                alt={currentUser.name}
                className="w-9 h-9 rounded-full"
                data-oid="bkfn9em"
              />
            ) : (
              <div
                className="w-9 h-9 rounded-full bg-slate-300 dark:bg-slate-600 flex items-center justify-center text-slate-500 dark:text-slate-300 font-semibold"
                data-oid="wk2mxaj"
              >
                {currentUser.name.substring(0, 1).toUpperCase()}
              </div>
            )}
          </button>
        ) : !isAuthenticated ? (
          <button
            onClick={openSignInModal}
            className="px-4 py-2 bg-[#222428] dark:bg-slate-700 text-white text-sm font-medium rounded-lg hover:bg-opacity-90 dark:hover:bg-slate-600 transition-colors"
            data-oid="j1av98w"
          >
            {t("signIn")}
          </button>
        ) : null}
      </div>
      {isAuthenticated && isHeaderUserMenuOpen && (
        <UserProfileMenu
          isOpen={isHeaderUserMenuOpen}
          onClose={() => setIsHeaderUserMenuOpen(false)}
          triggerRef={headerAvatarRef}
          menuPosition="bottom-right"
          triggerButtonId={headerUserMenuButtonId}
          navigateTo={navigateTo}
          handleLogout={handleLogout}
          data-oid="r9q_ux4"
        />
      )}
    </>
  );
};

const WelcomeScreen: React.FC<{
  onSendMessage: (message: string, useWebSearch: boolean) => void;
  isLoading: boolean;
  isAuthenticated: boolean;
  currentUser: User;
}> = ({ onSendMessage, isLoading, isAuthenticated, currentUser }) => {
  const { t } = useTranslation();
  const welcomeText =
    isAuthenticated && currentUser.name
      ? t("welcomeMessage", { name: currentUser.name.split("(")[0].trim() })
      : t("welcomeMessageGuest");

  return (
    <div
      className="flex-1 flex flex-col items-center justify-center p-6 text-center"
      data-oid="a_2rt6."
    >
      {!isAuthenticated && (
        <ZLogoIcon
          className="w-16 h-16 text-slate-700 dark:text-slate-300 mb-6"
          data-oid="08yj91:"
        />
      )}
      <h1
        className="text-3xl sm:text-4xl font-semibold text-slate-800 dark:text-slate-100 mb-3"
        data-oid="zmae4vj"
      >
        {welcomeText}
      </h1>
      <ChatInput
        onSendMessage={onSendMessage}
        isLoading={isLoading}
        showSuggestions={true}
        data-oid="poki0ww"
      />
    </div>
  );
};

const Footer: React.FC<{ isAuthenticated: boolean }> = ({
  isAuthenticated,
}) => {
  const { t } = useTranslation();
  return (
    <div
      className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-5 p-4 text-slate-500 dark:text-slate-400 bg-slate-50 dark:bg-slate-800 flex-shrink-0"
      data-oid="w.x0uw:"
    >
      {!isAuthenticated && (
        <div className="text-xs text-center sm:text-left" data-oid="zi:t8g3">
          <span data-oid="gzbc32v">{t("footerGuestText")}</span>
          <span className="hidden sm:inline mx-2" data-oid="j:396oa">
            |
          </span>
        </div>
      )}
      <div className="flex items-center space-x-5" data-oid="ypdjuvu">
        <a
          href="https://github.com/srgryzhkov/gemini-ai-chat-interface"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="Github"
          data-oid="i0fijke"
        >
          <GithubIcon data-oid="52o0870" />
        </a>
        <a
          href="https://ai.google.dev/gemini-api/docs/models/gemini-2.5-flash"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="AI Model Info"
          data-oid="6jt15ys"
        >
          <BrainIcon data-oid="oc9t4s0" />
        </a>
        <a
          href="https://x.com/srgryzhkov"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="Twitter / X"
          data-oid="2q2y14r"
        >
          <TwitterXIcon data-oid="srl3tpk" />
        </a>
        <a
          href="https://discordapp.com/users/358585938432327680"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-slate-700 dark:hover:text-slate-200"
          aria-label="Discord"
          data-oid="6riat.2"
        >
          <DiscordIcon data-oid="svp2nh6" />
        </a>
      </div>
    </div>
  );
};

interface MainAreaProps {
  activeChatId: string | null;
  setActiveChatId: (id: string | null) => void;
  chatMessages: Message[];
  onUpdateChat: (
    chatId: string,
    messages: Message[],
    details?: { isNew?: boolean; title?: string },
  ) => void;
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
  onNewChat: () => void;
  navigateTo: (view: View) => void;
  isAuthenticated: boolean;
  currentUser: User;
  handleLogout: () => void;
  openSignInModal: () => void;
}

const MainArea: React.FC<MainAreaProps> = ({
  activeChatId,
  setActiveChatId,
  chatMessages,
  onUpdateChat,
  isSidebarOpen,
  onToggleSidebar,
  onNewChat,
  navigateTo,
  isAuthenticated,
  currentUser,
  handleLogout,
  openSignInModal,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState<Model>(
    MODELS.find((m) => m.id === GEMINI_MODEL_NAME) || MODELS[0],
  );

  useEffect(() => {
    if (activeChatId) {
      setMessages(chatMessages);
    } else {
      setMessages([]);
      resetChatSession();
    }
  }, [activeChatId, chatMessages]);

  const handleModelChange = (modelId: string) => {
    const newModel = MODELS.find((m) => m.id === modelId);
    if (newModel) {
      setSelectedModel(newModel);
      setMessages([]);
      resetChatSession();
    }
  };

  const generateTitleFromMessage = (text: string): string => {
    return (
      text.split(" ").slice(0, 5).join(" ") +
      (text.split(" ").length > 5 ? "..." : "")
    );
  };

  const handleSendMessage = useCallback(
    async (inputText: string, useWebSearch: boolean) => {
      if (!currentUser) return;

      setIsLoading(true);
      const userMessageId = `user-${Date.now()}`;
      const aiMessageId = `ai-${Date.now() + 1}`;

      const isNewChatSession = !activeChatId;
      const currentChatIdToUse = activeChatId || `session-${Date.now()}`;

      if (isNewChatSession) {
        setActiveChatId(currentChatIdToUse);
      }

      const newUserMessage: Message = {
        id: userMessageId,
        text: inputText,
        sender: "user",
        timestamp: new Date(),
        avatar: currentUser.avatarUrl,
        chatId: currentChatIdToUse,
      };

      const updatedMessagesAfterUser = [...messages, newUserMessage];
      setMessages(updatedMessagesAfterUser);

      const aiPlaceholderMessage: Message = {
        id: aiMessageId,
        text: "",
        sender: "ai",
        timestamp: new Date(),
        isLoading: true,
        chatId: currentChatIdToUse,
      };
      const updatedMessagesWithPlaceholder = [
        ...updatedMessagesAfterUser,
        aiPlaceholderMessage,
      ];

      setMessages(updatedMessagesWithPlaceholder);

      let accumulatedAiText = "";
      let finalGroundingChunksFromStream: any[] | undefined;
      let finalMessagesForApp: Message[] = updatedMessagesWithPlaceholder;

      try {
        const stream = sendMessageToGeminiStream(inputText, useWebSearch);

        for await (const chunk of stream) {
          if (chunk.error) {
            accumulatedAiText = `Error: ${chunk.error}`;
            finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
              msg.id === aiMessageId
                ? {
                    ...msg,
                    text: accumulatedAiText,
                    isLoading: false,
                    isError: true,
                  }
                : msg,
            );
            setMessages(finalMessagesForApp);
            break;
          }
          if (chunk.textChunk) {
            accumulatedAiText += chunk.textChunk;
          }
          if (chunk.finalGroundingChunks) {
            finalGroundingChunksFromStream = chunk.finalGroundingChunks;
          }
          finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
            msg.id === aiMessageId
              ? {
                  ...msg,
                  text: accumulatedAiText,
                  isLoading: true,
                  groundingChunks:
                    finalGroundingChunksFromStream || msg.groundingChunks,
                }
              : msg,
          );
          setMessages(finalMessagesForApp);
        }

        finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
          msg.id === aiMessageId
            ? {
                ...msg,
                text: accumulatedAiText,
                isLoading: false,
                isError: !!finalMessagesForApp.find(
                  (m) => m.id === aiMessageId && m.isError,
                ),
                groundingChunks:
                  finalGroundingChunksFromStream || msg.groundingChunks,
              }
            : msg,
        );
        setMessages(finalMessagesForApp);

        onUpdateChat(currentChatIdToUse, finalMessagesForApp, {
          isNew: isNewChatSession,
          title: isNewChatSession
            ? generateTitleFromMessage(inputText)
            : undefined,
        });
      } catch (error) {
        console.error("Error handling stream:", error);
        const errorText =
          error instanceof Error ? error.message : "Failed to get response.";
        finalMessagesForApp = updatedMessagesWithPlaceholder.map((msg) =>
          msg.id === aiMessageId
            ? { ...msg, text: errorText, isLoading: false, isError: true }
            : msg,
        );
        setMessages(finalMessagesForApp);
        onUpdateChat(currentChatIdToUse, finalMessagesForApp, {
          isNew: isNewChatSession,
          title: isNewChatSession
            ? generateTitleFromMessage(inputText)
            : undefined,
        });
      } finally {
        setIsLoading(false);
      }
    },
    [activeChatId, setActiveChatId, currentUser, messages, onUpdateChat],
  );

  return (
    <div
      className="flex-1 flex flex-col bg-slate-50 dark:bg-slate-900 min-h-0"
      data-oid="0mie:v2"
    >
      <MainHeader
        selectedModel={selectedModel}
        onModelChange={handleModelChange}
        isSidebarOpen={isSidebarOpen}
        onToggleSidebar={onToggleSidebar}
        onNewChat={onNewChat}
        navigateTo={navigateTo}
        isAuthenticated={isAuthenticated}
        currentUser={currentUser}
        handleLogout={handleLogout}
        openSignInModal={openSignInModal}
        data-oid="xys1yk3"
      />

      {messages.length === 0 && !activeChatId ? (
        <div className="flex-1 flex flex-col min-h-0" data-oid="6pm5mmr">
          <WelcomeScreen
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            isAuthenticated={isAuthenticated}
            currentUser={currentUser}
            data-oid="75vb0hf"
          />

          <Footer isAuthenticated={isAuthenticated} data-oid="-r92twn" />
        </div>
      ) : (
        <div
          className="flex-1 flex flex-col min-h-0 overflow-hidden"
          data-oid="ce8-4c."
        >
          <div className="flex-1 overflow-y-auto" data-oid="q2b_r9s">
            <MessageDisplay
              messages={messages}
              isAuthenticated={isAuthenticated}
              currentUser={currentUser}
              data-oid="q68372v"
            />
          </div>
          <div
            className="p-6 bg-slate-50 dark:bg-slate-900 border-t border-slate-200 dark:border-slate-700/50 flex-shrink-0"
            data-oid="3p5mcap"
          >
            <ChatInput
              onSendMessage={handleSendMessage}
              isLoading={isLoading}
              data-oid="m32kuux"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default MainArea;
