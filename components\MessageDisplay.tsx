import React from "react";
import { Message, User, GroundingChunk } from "../types";
import { useTranslation } from "react-i18next";

interface MessageDisplayProps {
  messages: Message[];
  isAuthenticated: boolean;
  currentUser: User;
}

const MessageDisplay: React.FC<MessageDisplayProps> = ({
  messages,
  isAuthenticated,
  currentUser,
}) => {
  const { t } = useTranslation();

  const formatTimestamp = (timestamp: Date) => {
    try {
      // Handle case where timestamp might be a string or invalid date
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return "Invalid time";
      }

      return new Intl.DateTimeFormat("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }).format(date);
    } catch (error) {
      console.warn("Error formatting timestamp:", error);
      return "Invalid time";
    }
  };

  const renderGroundingChunks = (groundingChunks?: GroundingChunk[]) => {
    if (!groundingChunks || groundingChunks.length === 0) return null;

    return (
      <div className="mt-3 space-y-2">
        <div className="text-xs text-slate-500 dark:text-slate-400 font-medium">
          {t("sources")}:
        </div>
        <div className="flex flex-wrap gap-2">
          {groundingChunks.map((chunk, index) => {
            const source = chunk.web || chunk.retrievedContext;
            if (!source) return null;

            return (
              <a
                key={index}
                href={source.uri}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-2 py-1 text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
              >
                <span className="truncate max-w-[200px]">{source.title}</span>
                <svg
                  className="w-3 h-3 ml-1 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
              </a>
            );
          })}
        </div>
      </div>
    );
  };

  const renderMessage = (message: Message) => {
    const isUser = message.sender === "user";
    const isAI = message.sender === "ai";

    return (
      <div
        key={message.id}
        className={`flex ${isUser ? "justify-end" : "justify-start"} mb-6`}
      >
        <div
          className={`flex ${isUser ? "flex-row-reverse" : "flex-row"} items-start space-x-3 ${isUser ? "space-x-reverse" : ""} max-w-[95%] sm:max-w-[85%]`}
        >
          {/* Avatar */}
          <div className="flex-shrink-0">
            {isUser ? (
              isAuthenticated && currentUser.avatarUrl ? (
                <img
                  src={currentUser.avatarUrl}
                  alt={currentUser.name}
                  className="w-8 h-8 rounded-full"
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold">
                  {isAuthenticated && currentUser.name
                    ? currentUser.name.substring(0, 1).toUpperCase()
                    : "U"}
                </div>
              )
            ) : (
              <div className="w-8 h-8 rounded-full bg-slate-600 dark:bg-slate-400 flex items-center justify-center">
                <svg
                  className="w-5 h-5 text-white dark:text-slate-800"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                </svg>
              </div>
            )}
          </div>

          {/* Message Content */}
          <div
            className={`flex flex-col ${isUser ? "items-end" : "items-start"}`}
          >
            <div
              className={`px-4 py-3 rounded-2xl ${
                isUser
                  ? "bg-blue-500 text-white"
                  : message.isError
                    ? "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800"
                    : "bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200 border border-slate-200 dark:border-slate-600"
              } ${isUser ? "rounded-br-md" : "rounded-bl-md"} shadow-sm`}
            >
              {message.isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                  </div>
                  <span className="text-sm text-slate-500 dark:text-slate-400">
                    {t("thinking")}
                  </span>
                </div>
              ) : (
                <div className="whitespace-pre-wrap break-words">
                  {message.text}
                </div>
              )}

              {/* Grounding chunks for AI messages */}
              {isAI && !message.isLoading && renderGroundingChunks(message.groundingChunks)}
            </div>

            {/* Timestamp */}
            <div
              className={`text-xs text-slate-500 dark:text-slate-400 mt-1 ${isUser ? "text-right" : "text-left"}`}
            >
              {formatTimestamp(message.timestamp)}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (messages.length === 0) {
    return null;
  }

  return (
    <div className="w-full max-w-3xl mx-auto">
      <div className="flex flex-col space-y-4 p-6">
        {messages.map(renderMessage)}
      </div>
    </div>
  );
};

export default MessageDisplay;