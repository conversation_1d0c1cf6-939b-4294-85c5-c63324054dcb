import React from "react";
import { Message, User, GroundingChunk } from "../types";
import { useTranslation } from "react-i18next";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import {
  EditMessageIcon,
  CopyMessageIcon,
  DeleteMessageIcon,
  GoodResponseIcon,
  BadResponseIcon,
  RegenerateIcon
} from "../constants";


interface MessageDisplayProps {
  messages: Message[];
  isAuthenticated: boolean;
  currentUser: User;
}

const MessageDisplay: React.FC<MessageDisplayProps> = ({
  messages,
  isAuthenticated,
  currentUser,
}) => {
  const { t } = useTranslation();

  const formatTimestamp = (timestamp: Date) => {
    try {
      // Handle case where timestamp might be a string or invalid date
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return "Invalid time";
      }

      return new Intl.DateTimeFormat("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      }).format(date);
    } catch (error) {
      console.warn("Error formatting timestamp:", error);
      return "Invalid time";
    }
  };

  const renderGroundingChunks = (groundingChunks?: GroundingChunk[]) => {
    if (!groundingChunks || groundingChunks.length === 0) return null;

    return (
      <div className="mt-3 space-y-2">
        <div className="text-xs text-slate-500 dark:text-slate-400 font-medium">
          {t("sources")}:
        </div>
        <div className="flex flex-wrap gap-2">
          {groundingChunks.map((chunk, index) => {
            const source = chunk.web || chunk.retrievedContext;
            if (!source) return null;

            return (
              <a
                key={index}
                href={source.uri}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-2 py-1 text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
              >
                <span className="truncate max-w-[200px]">{source.title}</span>
                <svg
                  className="w-3 h-3 ml-1 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
              </a>
            );
          })}
        </div>
      </div>
    );
  };

  const renderMessage = (message: Message) => {
    const isUser = message.sender === "user";
    const isAI = message.sender === "ai";

    return (
      <div
        key={message.id}
        className={`group flex ${isUser ? "justify-end" : "justify-start"} mb-6`}
      >
        <div
          className={`flex ${isUser ? "flex-row-reverse" : "flex-row"} items-start space-x-3 ${isUser ? "space-x-reverse" : ""} ${isUser ? "max-w-[90%] sm:max-w-[85%]" : "w-full"}`}
        >
          {/* Avatar */}
          <div className="flex-shrink-0">
            {isUser ? (
              isAuthenticated && currentUser.avatarUrl ? (
                <img
                  src={currentUser.avatarUrl}
                  alt={currentUser.name}
                  className="w-8 h-8 rounded-full"
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-semibold">
                  {isAuthenticated && currentUser.name
                    ? currentUser.name.substring(0, 1).toUpperCase()
                    : "U"}
                </div>
              )
            ) : (
              <div className="w-8 h-8 rounded-full bg-slate-600 dark:bg-slate-400 flex items-center justify-center">
                <svg
                  className="w-5 h-5 text-white dark:text-slate-800"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                </svg>
              </div>
            )}
          </div>

          {/* Message Content */}
          <div
            className={`flex flex-col ${isUser ? "items-end" : "items-start"} ${isUser ? "" : "flex-1"}`}
          >
            <div className="relative group w-full">
              <div
                className={`px-4 py-3 rounded-2xl ${
                  isUser
                    ? "bg-white dark:bg-slate-700 text-slate-800 dark:text-white border shadow-sm border-slate-200 dark:border-slate-600"
                    : message.isError
                      ? "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800"
                      : "bg-[#F8FAFC] dark:bg-[#0F172A] text-slate-800 dark:text-slate-200"
                } ${isUser ? "rounded-br-md" : "rounded-bl-md"} ${isUser ? "" : "mr-8 sm:mr-11"}`}
              >
                {message.isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                      <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                    </div>
                    <span className="text-sm text-slate-500 dark:text-slate-400">
                      {t("thinking")}
                    </span>
                  </div>
                ) : (
                  <div className="break-words">
                    {isAI ? (
                      <div className="prose prose-base max-w-none dark:prose-invert prose-headings:mt-4 prose-headings:mb-2 prose-p:my-2 prose-p:text-base prose-ul:my-2 prose-ol:my-2 prose-li:my-0 prose-pre:my-3 prose-pre:bg-slate-100 dark:prose-pre:bg-slate-800 prose-pre:text-slate-800 dark:prose-pre:text-slate-200 prose-pre:border prose-pre:border-slate-200 dark:prose-pre:border-slate-600 prose-code:bg-slate-100 dark:prose-code:bg-slate-800 prose-code:text-slate-800 dark:prose-code:text-slate-200 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:before:content-none prose-code:after:content-none prose-code:text-sm prose-blockquote:border-l-slate-300 dark:prose-blockquote:border-l-slate-600 prose-blockquote:my-3">
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {message.text}
                        </ReactMarkdown>
                      </div>
                    ) : (
                      <div className="whitespace-pre-wrap">
                        {message.text}
                      </div>
                    )}
                  </div>
                )}

                {/* Grounding chunks for AI messages */}
                {isAI && !message.isLoading && renderGroundingChunks(message.groundingChunks)}
              </div>
            {/* Timestamp and Action Icons */}
            <div className={`flex items-center gap-2 w-full text-xs text-slate-500 dark:text-slate-400 ${isUser ? "justify-end mt-1" : "justify-start mt-1"}`}>
              <div className="relative min-w-[80px] flex items-center">
                <span className={`transition-opacity duration-200 ${isUser ? "text-right" : "text-left"} group-hover:opacity-0 opacity-100`}>
                  {formatTimestamp(message.timestamp)}
                </span>
                <div className={`absolute left-0 right-0 flex gap-1 transition-opacity duration-200 opacity-0 group-hover:opacity-100 ${isUser ? "justify-end" : "justify-start"}`}>
                  {isUser && !message.isLoading && (
                    <>
                      <button
                        title="Edit"
                        className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"
                      >
                        <EditMessageIcon className="w-4 h-4" />
                      </button>
                      <button
                        title="Copy"
                        className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"
                      >
                        <CopyMessageIcon className="w-4 h-4" />
                      </button>
                      <button
                        title="Delete"
                        className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                      >
                        <DeleteMessageIcon className="w-4 h-4" />
                      </button>
                    </>
                  )}
                  {isAI && !message.isLoading && (
                    <>
                      <button
                        title="Good response"
                        className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 transition-colors"
                      >
                        <GoodResponseIcon className="w-4 h-4" />
                      </button>
                      <button
                        title="Bad response"
                        className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
                      >
                        <BadResponseIcon className="w-4 h-4" />
                      </button>
                      <button
                        title="Regenerate"
                        className="p-1 rounded hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 transition-colors"
                      >
                        <RegenerateIcon className="w-4 h-4" />
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (messages.length === 0) {
    return null;
  }

  return (
    <div className="w-full max-w-3xl mx-auto message-scrollbar">
      <div className="flex flex-col space-y-4 p-6">
        {messages.map(renderMessage)}
      </div>
    </div>
  );
};

export default MessageDisplay;