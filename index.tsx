import React from "react";
import <PERSON>actD<PERSON> from "react-dom/client";
import App from "./App";
import { I18nextProvider } from "react-i18next";
import i18n, { initializeI18n } from "./i18n"; // Import the i18n instance and the new initializer

const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);

// Initialize i18next and then render the app
initializeI18n()
  .then((initializedI18nInstance) => {
    console.log("[App] i18next initialized successfully.");
    root.render(
      <React.StrictMode>
        <I18nextProvider i18n={initializedI18nInstance}>
          <App />
        </I18nextProvider>
      </React.StrictMode>,
    );
  })
  .catch((error) => {
    console.error(
      "Critical error during i18n initialization. App might not function as expected.",
      error,
    );
    // Optionally render a user-friendly error message to the DOM
    root.render(
      <div
        style={{
          padding: "20px",
          textAlign: "center",
          fontFamily: "sans-serif",
          color: "#333",
        }}
      >
        <h1>Application Error</h1>
        <p>
          There was an issue loading essential application resources
          (translations).
        </p>
        <p>
          Please try refreshing the page. If the problem persists, contact
          support.
        </p>
      </div>,
    );
  });
