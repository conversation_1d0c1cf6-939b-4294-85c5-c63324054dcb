import React from "react";
import AssistantsModalLauncher from "./AssistantsModalLauncher";
import { useTranslation } from "react-i18next";
import { View, SettingsSubView, Theme } from "../App";
import {
  SidebarToggleIcon,
  SettingsIcon as PageSettingsIcon,
  GlobeAltIcon,
  MoonIcon,
  CloseIcon,
} from "../constants";

const UserCircleIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className || "w-5 h-5"}
    data-oid="73_aowz"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
      data-oid="lmn4dv8"
    />
  </svg>
);

interface SettingsPageProps {
  navigateTo: (view: View, subView?: SettingsSubView) => void;
  isSmallScreen: boolean;
  openAccountModal: () => void;
  settingsSubView: SettingsSubView;
  currentTheme: Theme;
  onThemeChange: (theme: Theme) => void;
}

interface SettingsPageContentProps
  extends Pick<
    SettingsPageProps,
    | "navigateTo"
    | "isSmallScreen"
    | "openAccountModal"
    | "currentTheme"
    | "onThemeChange"
  > {}

const SettingsPageContent: React.FC<SettingsPageContentProps> = ({
  navigateTo,
  isSmallScreen,
  openAccountModal,
  currentTheme,
  onThemeChange,
}) => {
  const { t, i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  const currentLanguage = i18n.language;

  const languages = [
    { code: "en", name: "English" },
    { code: "ru", name: "Русский" },
  ];

  const themes: { id: Theme; name: string }[] = [
    { id: "light", name: t("lightTheme") },
    { id: "dark", name: t("darkTheme") },
    { id: "system", name: t("systemTheme") },
  ];

  return (
    <>
      {/* Assistants Section */}
      <section aria-labelledby="assistants-settings-heading" className="mb-8">
        <div className="flex items-center mb-4">
          <span className="w-6 h-6 mr-3 text-slate-500 dark:text-slate-400 text-2xl">🤖</span>
          <h2 id="assistants-settings-heading" className="text-lg font-semibold text-slate-700 dark:text-slate-200">
            {t("assistants", "Assistants")}
          </h2>
        </div>
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow border border-slate-200 dark:border-slate-700">
          <p className="text-sm text-slate-600 dark:text-slate-300 mb-3">
            {t("assistantsSectionDescription", "Manage your custom AI assistants.")}
          </p>
          <AssistantsModalLauncher />
        </div>
      </section>
      {/* Language Section */}
      <section aria-labelledby="language-settings-heading" data-oid="mnopwe7">
        <div className="flex items-center mb-4" data-oid="b0ehvvs">
          <GlobeAltIcon
            className="w-6 h-6 mr-3 text-slate-500 dark:text-slate-400"
            data-oid="uevw2ud"
          />

          <h2
            id="language-settings-heading"
            className="text-lg font-semibold text-slate-700 dark:text-slate-200"
            data-oid="kyglq5w"
          >
            {t("language")}
          </h2>
        </div>
        <div
          className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow border border-slate-200 dark:border-slate-700"
          data-oid=".lyrhrk"
        >
          <p
            className="text-sm text-slate-600 dark:text-slate-300 mb-3"
            data-oid="i9-p3l-"
          >
            {t("languageSectionDescription")}
          </p>
          <div className="flex flex-wrap gap-3" data-oid="it1_3vk">
            {languages.map((lang) => (
              <button
                key={lang.code}
                onClick={() => changeLanguage(lang.code)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors
                  ${
                    currentLanguage.startsWith(lang.code)
                      ? "bg-slate-600 hover:bg-slate-700 dark:bg-[#2563EB] dark:hover:bg-blue-700 text-white dark:text-white"
                      : "bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200"
                  }`}
                data-oid="-:2njv3"
              >
                {lang.name}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Appearance Section */}
      <section aria-labelledby="appearance-settings-heading" data-oid="nv9m:vn">
        <div className="flex items-center mb-4" data-oid="3tjsysj">
          <MoonIcon
            className="w-6 h-6 mr-3 text-slate-500 dark:text-slate-400"
            data-oid="bkyt9wn"
          />

          <h2
            id="appearance-settings-heading"
            className="text-lg font-semibold text-slate-700 dark:text-slate-200"
            data-oid="d-5f.gm"
          >
            {t("appearance")}
          </h2>
        </div>
        <div
          className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow border border-slate-200 dark:border-slate-700"
          data-oid="sqh3ugp"
        >
          <p
            className="text-sm font-medium text-slate-600 dark:text-slate-300 mb-1"
            data-oid=":w_g0jf"
          >
            {t("theme")}
          </p>
          <p
            className="text-xs text-slate-500 dark:text-slate-400 mb-3"
            data-oid="cxtde29"
          >
            {t("themeSectionDescription")}
          </p>
          <div className="flex flex-wrap gap-3" data-oid="7yie2:k">
            {themes.map((theme) => (
              <button
                key={theme.id}
                onClick={() => onThemeChange(theme.id)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors
                      ${
                        currentTheme === theme.id
                          ? "bg-slate-600 hover:bg-slate-700 dark:bg-[#2563EB] dark:hover:bg-blue-700 text-white dark:text-white"
                          : "bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200"
                      }
                      `}
                data-oid="92xz5:2"
              >
                {theme.name}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Account Section */}
      <section aria-labelledby="account-settings-heading" data-oid="scbp4u:">
        <div className="flex items-center mb-4" data-oid="lwnzkug">
          <UserCircleIcon
            className="w-6 h-6 mr-3 text-slate-500 dark:text-slate-400"
            data-oid="rg8auq."
          />

          <h2
            id="account-settings-heading"
            className="text-lg font-semibold text-slate-700 dark:text-slate-200"
            data-oid="_iarfin"
          >
            {t("account")}
          </h2>
        </div>
        <div
          className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow border border-slate-200 dark:border-slate-700"
          data-oid="f1u.:d2"
        >
          <p
            className="text-sm text-slate-600 dark:text-slate-300 mb-3"
            data-oid="lg_:-3t"
          >
            {t(
              "manageAccountDescription",
              "Manage your account details and subscription.",
            )}
          </p>
          <button
            onClick={openAccountModal}
            className="px-4 py-2 bg-slate-600 hover:bg-slate-700 dark:bg-slate-700 dark:hover:bg-slate-600 text-white dark:text-slate-100 rounded-md text-sm font-medium transition-colors"
            data-oid="rc7_2n:"
          >
            {t("editAccountDetails")}
          </button>
        </div>
      </section>

      {isSmallScreen && (
        <div className="mt-10 text-center" data-oid="nhrd95c">
          <button
            onClick={() => navigateTo("chat", "main")}
            className="px-6 py-2.5 bg-slate-700 hover:bg-slate-800 dark:bg-slate-600 dark:hover:bg-slate-500 text-white dark:text-slate-100 rounded-lg text-sm font-medium transition-colors"
            data-oid="f52cxdw"
          >
            {t("backToChat")}
          </button>
        </div>
      )}
    </>
  );
};

const SettingsPage: React.FC<SettingsPageProps> = ({
  navigateTo,
  isSmallScreen,
  openAccountModal,
  settingsSubView,
  currentTheme,
  onThemeChange,
}) => {
  const { t } = useTranslation();

  if (!isSmallScreen) {
    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-60 dark:bg-black/70 z-[55] flex items-center justify-center p-4 transition-opacity duration-300 ease-in-out"
        onClick={(e) => {
          if (e.target === e.currentTarget) navigateTo("chat", "main");
        }}
        role="dialog"
        aria-modal="true"
        aria-labelledby="settings-dialog-title"
        data-oid="qk:ovcz"
      >
        <div
          className="bg-slate-50 dark:bg-slate-800/95 text-slate-800 dark:text-slate-200 rounded-xl shadow-2xl overflow-hidden w-full max-w-2xl flex flex-col max-h-[calc(100vh-4rem)] backdrop-blur-sm"
          data-oid=":ff9jth"
        >
          <div
            className="flex items-center justify-between p-5 h-16 border-b border-slate-200 dark:border-slate-700 bg-white/70 dark:bg-slate-800/70 flex-shrink-0"
            data-oid="p_kst8d"
          >
            <div className="flex items-center space-x-3" data-oid="fxlxtws">
              <PageSettingsIcon
                className="w-6 h-6 text-slate-700 dark:text-slate-300"
                data-oid="y6klh4t"
              />

              <h1
                id="settings-dialog-title"
                className="text-xl font-semibold"
                data-oid="mpij09m"
              >
                {t("settingsPageTitle")}
              </h1>
            </div>
            <button
              onClick={() => navigateTo("chat", "main")}
              className="p-1.5 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full"
              aria-label={t("closeSettings")}
              data-oid="u:9t0m2"
            >
              <CloseIcon className="w-5 h-5" data-oid="6h:k16:" />
            </button>
          </div>
          <div
            className="flex-1 overflow-y-auto p-6 md:p-8 space-y-8 settings-scrollbar"
            data-oid="-bhg2it"
          >
            <SettingsPageContent
              navigateTo={navigateTo}
              isSmallScreen={isSmallScreen}
              openAccountModal={openAccountModal}
              currentTheme={currentTheme}
              onThemeChange={onThemeChange}
              data-oid="xqlo:sa"
            />
          </div>
        </div>
      </div>
    );
  }

  if (settingsSubView !== "main") {
    return null;
  }

  return (
    <div
      className="flex-1 flex flex-col bg-slate-50 dark:bg-slate-900 min-h-0 text-slate-800 dark:text-slate-200"
      data-oid="mcpxj:6"
    >
      <div
        className="flex items-center justify-between p-5 h-16 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 flex-shrink-0"
        data-oid="80uqa_y"
      >
        <div className="flex items-center space-x-3" data-oid="o6l0kz-">
          <button
            onClick={() => navigateTo("chat", "main")}
            className="p-2 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md md:hidden"
            aria-label={t("backToChat")}
            data-oid=":r99p0k"
          >
            <SidebarToggleIcon
              className="w-6 h-6 transform rotate-180"
              data-oid="up4m_s9"
            />
          </button>
          <PageSettingsIcon
            className="w-6 h-6 text-slate-700 dark:text-slate-300"
            data-oid="bkpbazv"
          />

          <h1 className="text-xl font-semibold" data-oid="tyb1xio">
            {t("settingsPageTitle")}
          </h1>
        </div>
      </div>
      <div
        className="flex-1 overflow-y-auto p-6 md:p-8 space-y-8 settings-scrollbar"
        data-oid="uyi3xsu"
      >
        <SettingsPageContent
          navigateTo={navigateTo}
          isSmallScreen={isSmallScreen}
          openAccountModal={openAccountModal}
          currentTheme={currentTheme}
          onThemeChange={onThemeChange}
          data-oid="ado4h2v"
        />
      </div>
    </div>
  );
};

export default SettingsPage;
