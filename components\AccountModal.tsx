import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { User } from "../types";
import {
  CloseIcon,
  UploadIcon,
  UserCircleIcon as ProfileSectionIcon,
  LockClosedIcon,
  EyeIcon,
  EyeSlashIcon,
  SidebarToggleIcon,
} from "../constants";

interface AccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedUser: Partial<User>) => void;
  currentUser: User;
  isSmallScreen: boolean;
}

const AccountFormContent: React.FC<
  Pick<AccountModalProps, "currentUser" | "onSave" | "onClose">
> = ({ currentUser, onSave, onClose }) => {
  const { t } = useTranslation();
  const [name, setName] = useState(currentUser.name);
  const [email, setEmail] = useState(currentUser.email || "");
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);

  const profilePictureInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setName(currentUser.name);
    setEmail(currentUser.email || "");
  }, [currentUser]);

  const handleSaveChanges = (e: React.FormEvent) => {
    e.preventDefault();
    if (newPassword && newPassword !== confirmNewPassword) {
      alert(t("passwordsDoNotMatch"));
      return;
    }
    onSave({ name, email });
    if (currentPassword && newPassword) {
      console.log("Password change attempt (simulated):", {
        currentPassword,
        newPassword,
      });
    }
  };

  const handleProfilePictureUpload = () => {
    profilePictureInputRef.current?.click();
  };

  const onFileSelected = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      console.log("Selected profile picture (simulated upload):", file.name);
    }
  };

  const renderPasswordField = (
    labelKey: string,
    value: string,
    setter: React.Dispatch<React.SetStateAction<string>>,
    isVisible: boolean,
    toggleVisibility: () => void,
    id: string,
    placeholderKey?: string,
  ) => (
    <div data-oid="2q9-meo">
      <label
        htmlFor={id}
        className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1"
        data-oid="sei28c-"
      >
        {t(labelKey)}
      </label>
      <div className="relative" data-oid="c7s4i8i">
        <input
          id={id}
          type={isVisible ? "text" : "password"}
          value={value}
          onChange={(e) => setter(e.target.value)}
          placeholder={t(placeholderKey || labelKey)}
          className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500"
          data-oid="do2sgsr"
        />

        <button
          type="button"
          onClick={toggleVisibility}
          className="absolute inset-y-0 right-0 px-3 flex items-center text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200"
          aria-label={
            isVisible
              ? t("passwordVisibilityHide")
              : t("passwordVisibilityShow")
          }
          data-oid="gh3xyi6"
        >
          {isVisible ? (
            <EyeSlashIcon className="w-5 h-5" data-oid="hfl8w6r" />
          ) : (
            <EyeIcon className="w-5 h-5" data-oid="om3n10d" />
          )}
        </button>
      </div>
    </div>
  );

  return (
    <form onSubmit={handleSaveChanges} className="space-y-6" data-oid="jsmb3.2">
      <section aria-labelledby="profile-picture-heading" data-oid="_p4:.r9">
        <div className="flex items-center mb-1" data-oid="u2sxh5d">
          <ProfileSectionIcon
            className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400"
            data-oid="wo2u2d6"
          />

          <h3
            id="profile-picture-heading"
            className="text-sm font-medium text-slate-600 dark:text-slate-300"
            data-oid="pvcqrpt"
          >
            {t("profilePicture")}
          </h3>
        </div>
        <div className="flex items-center space-x-4" data-oid="di4ylgv">
          {currentUser.avatarUrl ? (
            <img
              src={currentUser.avatarUrl}
              alt={currentUser.name}
              className="w-16 h-16 rounded-full"
              data-oid="g22jev."
            />
          ) : (
            <div
              className="w-16 h-16 rounded-full bg-slate-300 dark:bg-slate-600 flex items-center justify-center text-slate-500 dark:text-slate-300 text-xl font-semibold"
              data-oid="b73ht60"
            >
              {currentUser.name
                ? currentUser.name.substring(0, 1).toUpperCase()
                : "G"}
            </div>
          )}
          <input
            type="file"
            accept="image/*"
            ref={profilePictureInputRef}
            onChange={onFileSelected}
            className="hidden"
            data-oid="hzc6nli"
          />

          <button
            type="button"
            onClick={handleProfilePictureUpload}
            className="px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm font-medium text-slate-700 dark:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700 flex items-center"
            data-oid="l4xrj76"
          >
            <UploadIcon className="w-4 h-4 mr-2" data-oid="kvpae5z" />
            {t("upload")}
          </button>
        </div>
      </section>

      <section aria-labelledby="personal-info-heading" data-oid="w3yx58w">
        <div className="flex items-center mb-1" data-oid="qkm78:v">
          <ProfileSectionIcon
            className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400"
            data-oid="9weqyw5"
          />

          <h3
            id="personal-info-heading"
            className="text-sm font-medium text-slate-600 dark:text-slate-300"
            data-oid="pt45__n"
          >
            {t("personalInformation")}
          </h3>
        </div>
        <div className="space-y-4" data-oid="uh_a4qx">
          <div data-oid="hodf6-a">
            <label
              htmlFor="fullName"
              className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1"
              data-oid="4tsul-n"
            >
              {t("fullName")}
            </label>
            <input
              id="fullName"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder={t("fullName")}
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500"
              data-oid="q1un16i"
            />
          </div>
          <div data-oid="vyhyf.4">
            <label
              htmlFor="emailAddress"
              className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1"
              data-oid="yovi7lj"
            >
              {t("emailAddress")}
            </label>
            <input
              id="emailAddress"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder={t("emailAddress")}
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-slate-700 text-slate-900 dark:text-slate-100 placeholder-slate-400 dark:placeholder-slate-500"
              data-oid="frv_txh"
            />
          </div>
        </div>
      </section>
      <hr
        className="border-slate-200 dark:border-slate-700"
        data-oid="4uzb:dn"
      />

      <section aria-labelledby="change-password-heading" data-oid="tsc09i8">
        <div className="flex items-center mb-1" data-oid="z0l7rk6">
          <LockClosedIcon
            className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400"
            data-oid="86vp35q"
          />

          <h3
            id="change-password-heading"
            className="text-sm font-medium text-slate-600 dark:text-slate-300"
            data-oid="aaqks79"
          >
            {t("changePassword")}
          </h3>
        </div>
        <div className="space-y-4" data-oid="kgcjvsh">
          {renderPasswordField(
            "currentPassword",
            currentPassword,
            setCurrentPassword,
            showCurrentPassword,
            () => setShowCurrentPassword((s) => !s),
            "currentPassword",
            "currentPassword",
          )}
          {renderPasswordField(
            "newPassword",
            newPassword,
            setNewPassword,
            showNewPassword,
            () => setShowNewPassword((s) => !s),
            "newPassword",
            "newPassword",
          )}
          {renderPasswordField(
            "confirmNewPassword",
            confirmNewPassword,
            setConfirmNewPassword,
            showConfirmNewPassword,
            () => setShowConfirmNewPassword((s) => !s),
            "confirmNewPassword",
            "confirmNewPassword",
          )}
        </div>
      </section>

      <div
        className="flex flex-col sm:flex-row-reverse gap-3 pt-2"
        data-oid="m7woyvg"
      >
        <button
          type="submit"
          className="w-full sm:w-auto px-6 py-2.5 bg-slate-700 hover:bg-slate-800 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
          data-oid="qtu1gr2"
        >
          {t("saveChanges")}
        </button>
        <button
          type="button"
          onClick={onClose}
          className="w-full sm:w-auto px-6 py-2.5 border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 hover:bg-slate-50 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 rounded-lg text-sm font-medium transition-colors"
          data-oid="c_bcb-t"
        >
          {t("cancel")}
        </button>
      </div>
    </form>
  );
};

const AccountModal: React.FC<AccountModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentUser,
  isSmallScreen,
}) => {
  const { t } = useTranslation();

  if (!isOpen && !isSmallScreen) return null;
  if (isSmallScreen && !isOpen) return null;

  if (isSmallScreen) {
    return (
      <div
        className="flex-1 flex flex-col bg-slate-50 dark:bg-slate-900 min-h-0 text-slate-800 dark:text-slate-200"
        data-oid="6kmiuk8"
      >
        <div
          className="flex items-center justify-between p-5 h-16 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 flex-shrink-0"
          data-oid="m5jh6vy"
        >
          <div className="flex items-center space-x-3" data-oid="__:w8gl">
            <button
              onClick={onClose}
              className="p-2 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md"
              aria-label={t("backToChat")}
              data-oid="xyopjor"
            >
              <SidebarToggleIcon
                className="w-6 h-6 transform rotate-180"
                data-oid="ad2a7k2"
              />
            </button>
            <ProfileSectionIcon
              className="w-6 h-6 text-slate-700 dark:text-slate-300"
              data-oid="sgtm0n7"
            />

            <h1 className="text-xl font-semibold" data-oid="nv4ksah">
              {t("accountModalTitle")}
            </h1>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto p-6 md:p-8" data-oid="4:eypct">
          <AccountFormContent
            currentUser={currentUser}
            onSave={onSave}
            onClose={onClose}
            data-oid="4jkv89f"
          />
        </div>
      </div>
    );
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-60 dark:bg-black/70 z-[75] flex items-center justify-center p-4 transition-opacity duration-300 ease-in-out"
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
      role="dialog"
      aria-modal="true"
      aria-labelledby="account-dialog-title"
      data-oid="g6-pk:v"
    >
      <div
        className="bg-white dark:bg-slate-800/95 text-slate-800 dark:text-slate-200 rounded-xl shadow-2xl overflow-hidden w-full max-w-lg flex flex-col max-h-[calc(100vh-4rem)] backdrop-blur-sm"
        data-oid="qg75wro"
      >
        <div
          className="flex items-center justify-between p-5 h-16 border-b border-slate-200 dark:border-slate-700 bg-white/70 dark:bg-slate-800/70 flex-shrink-0"
          data-oid="3ky3.4s"
        >
          <div className="flex items-center space-x-3" data-oid="n0undoq">
            <ProfileSectionIcon
              className="w-6 h-6 text-slate-700 dark:text-slate-300"
              data-oid="768novh"
            />

            <h1
              id="account-dialog-title"
              className="text-xl font-semibold"
              data-oid="mak00dr"
            >
              {t("accountModalTitle")}
            </h1>
          </div>
          <button
            onClick={onClose}
            className="p-1.5 text-slate-400 dark:text-slate-300 hover:text-slate-600 dark:hover:text-slate-100 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-full"
            aria-label={t("closeSettings")}
            data-oid="ghegd:d"
          >
            <CloseIcon className="w-5 h-5" data-oid="y0b4kvz" />
          </button>
        </div>
        <div className="flex-1 overflow-y-auto p-6 md:p-8 account-scrollbar" data-oid="w6s6zcd">
          <AccountFormContent
            currentUser={currentUser}
            onSave={onSave}
            onClose={onClose}
            data-oid="j7q:oks"
          />
        </div>
      </div>
    </div>
  );
};

export default AccountModal;
