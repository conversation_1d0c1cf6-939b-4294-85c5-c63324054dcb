import React, { useEffect, useState } from "react";
import { Assistant, loadAssistants, saveAssistants, deleteAssistant } from "../types/assistant";
import { EditMessageIcon, DeleteMessageIcon } from "../constants";

interface AssistantsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: (assistant: Assistant) => void;
  onAdd: () => void;
}

const truncate = (str: string, max: number) =>
  str.length > max ? str.slice(0, max - 1) + "…" : str;

const AssistantsModal: React.FC<AssistantsModalProps> = ({ isOpen, onClose, onEdit, onAdd }) => {
  const [assistants, setAssistants] = useState<Assistant[]>([]);
  const [deleteId, setDeleteId] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) setAssistants(loadAssistants());
  }, [isOpen]);

  const handleDelete = (id: string) => {
    deleteAssistant(id);
    setAssistants(loadAssistants());
    setDeleteId(null);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
      <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl w-full max-w-3xl p-8 relative">
        <button className="absolute top-4 right-4 text-slate-400 hover:text-slate-700 dark:hover:text-white" onClick={onClose} aria-label="Close">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <h2 className="text-xl font-bold mb-6 text-slate-800 dark:text-white">Assistants</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          {assistants.map(a => (
            <div key={a.id} className="bg-slate-50 dark:bg-slate-700 rounded-xl p-4 flex flex-col shadow border border-slate-200 dark:border-slate-600 relative">
              <div className="flex items-center mb-2">
                <span className="text-2xl mr-2">{a.emoji}</span>
                <span className="font-semibold text-slate-800 dark:text-white flex-1 truncate">{a.name}</span>
                <button className="ml-2 p-1 hover:bg-slate-200 dark:hover:bg-slate-600 rounded" onClick={() => onEdit(a)} title="Edit"><EditMessageIcon className="w-4 h-4" /></button>
                <button className="ml-1 p-1 hover:bg-red-100 dark:hover:bg-red-900 rounded" onClick={() => setDeleteId(a.id)} title="Delete">
                  <svg className="w-4 h-4 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="text-slate-600 dark:text-slate-300 text-sm line-clamp-2 min-h-[2.5em]">{truncate(a.description, 80)}</div>
              {deleteId === a.id && (
                <div className="absolute inset-0 bg-black/40 flex flex-col items-center justify-center rounded-xl z-10">
                  <div className="bg-white dark:bg-slate-800 p-4 rounded shadow text-center">
                    <div className="mb-2 flex items-center justify-center gap-2">
                      <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                      Delete this assistant?
                    </div>
                    <div className="flex gap-2 justify-center">
                      <button className="px-3 py-1 rounded bg-red-500 text-white flex items-center gap-1" onClick={() => handleDelete(a.id)}>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Delete
                      </button>
                      <button className="px-3 py-1 rounded bg-slate-200 dark:bg-slate-700" onClick={() => setDeleteId(null)}>Cancel</button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
          {/* Add new assistant card */}
          <button onClick={onAdd} className="flex flex-col items-center justify-center border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-xl p-4 min-h-[120px] hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors">
            <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
            </svg>
            <span className="mt-2 text-slate-500 dark:text-slate-300 text-sm">Add Assistant</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AssistantsModal;
