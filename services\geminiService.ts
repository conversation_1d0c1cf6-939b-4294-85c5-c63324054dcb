

import { GoogleGenAI, GenerateContentResponse, Chat, SendMessageParameters, Candidate, GroundingChunk as GeminiGroundingChunk } from "@google/genai";
import { GEMINI_MODEL_NAME } from '../constants';
import { GroundingChunk } from "../types"; // This is the app's internal type

// Safely access process.env.API_KEY
const API_KEY_FROM_ENV = typeof process !== 'undefined' && process.env && typeof process.env.API_KEY === 'string'
  ? process.env.API_KEY
  : undefined;

const effectiveApiKey = API_KEY_FROM_ENV || "DUMMY_KEY_FOR_UI_DEV";

if (!API_KEY_FROM_ENV && effectiveApiKey === "DUMMY_KEY_FOR_UI_DEV") {
  console.warn("API_KEY environment variable not set or 'process' is not defined. Using dummy key for UI development. Gemini API calls will fail if a real key is needed and not provided by other means.");
} else if (!API_KEY_FROM_ENV && effectiveApiKey !== "DUMMY_KEY_FOR_UI_DEV") {
  // This case implies effectiveApiKey was set by some other means if DUMMY_KEY logic changes
  console.info("API_KEY environment variable not set or 'process' is not defined. Attempting to use a pre-configured API key.");
}


const ai = new GoogleGenAI({ apiKey: effectiveApiKey });
let chatInstance: Chat | null = null;

const initializeChat = (): Chat => {
  if (effectiveApiKey === "DUMMY_KEY_FOR_UI_DEV") {
     console.warn("Initializing chat with a dummy API key. Real API calls will not work.");
     // Potentially throw error or return a mock Chat interface if preferred for stricter dummy mode
  }
  return ai.chats.create({
    model: GEMINI_MODEL_NAME,
    config: {
      systemInstruction: 'You are a helpful and friendly AI assistant.',
    },
  });
};

const getChatInstance = (): Chat => {
  if (!chatInstance) {
    chatInstance = initializeChat();
  }
  return chatInstance;
};

export const resetChatSession = () => {
  chatInstance = null; 
}

const extractGroundingChunks = (candidate: Candidate | undefined): GroundingChunk[] => {
  if (!candidate || !candidate.groundingMetadata || !candidate.groundingMetadata.groundingChunks) {
    return [];
  }
  return candidate.groundingMetadata.groundingChunks.map((chunk: GeminiGroundingChunk): GroundingChunk => ({ // Map SDK type to app's internal type
    web: chunk.web ? { uri: chunk.web.uri || '', title: chunk.web.title || '' } : undefined,
    retrievedContext: chunk.retrievedContext ? { uri: chunk.retrievedContext.uri || '', title: chunk.retrievedContext.title || '' } : undefined,
  }));
};


export const sendMessageToGemini = async (
  messageText: string, 
  useWebSearch: boolean
): Promise<{ text: string; groundingChunks?: GroundingChunk[] }> => {
  if (effectiveApiKey === "DUMMY_KEY_FOR_UI_DEV") {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
    console.log(`Mock Gemini Call (dummy API key): message="${messageText}", webSearch=${useWebSearch}`);
    return { text: `Mock response (dummy API key). You said: "${messageText}". Web search: ${useWebSearch ? 'Enabled' : 'Disabled'}` };
  }

  try {
    const chat = getChatInstance();
    
    const request: SendMessageParameters = {
        message: messageText,
    };

    if (useWebSearch) {
        request.config = {
            ...(request.config || {}), 
            tools: [{ googleSearch: {} }],
        };
    }
    
    const response: GenerateContentResponse = await chat.sendMessage(request);
    const responseText = response.text;
    const groundingChunks = extractGroundingChunks(response.candidates?.[0]);

    return { text: responseText, groundingChunks };

  } catch (error) {
    console.error("Error sending message to Gemini:", error);
    if (error instanceof Error) {
        return { text: `Error: ${error.message}` };
    }
    return { text: "An unknown error occurred while contacting the AI." };
  }
};


export async function* sendMessageToGeminiStream(
  messageText: string,
  useWebSearch: boolean
): AsyncGenerator<{ textChunk?: string; finalGroundingChunks?: GroundingChunk[]; error?: string }> {
  if (effectiveApiKey === "DUMMY_KEY_FOR_UI_DEV") {
    console.log(`Mock Gemini Stream Call (dummy API key): message="${messageText}", webSearch=${useWebSearch}`);
    const mockMessage = `Mock stream response (dummy API key). You said: "${messageText}". Web search: ${useWebSearch ? 'Enabled' : 'Disabled'}`;
    // Simulate streaming
    for (let i = 0; i < mockMessage.length; i += 10) {
        yield { textChunk: mockMessage.substring(i, Math.min(i + 10, mockMessage.length)) };
        await new Promise(resolve => setTimeout(resolve, 50));
    }
    yield { finalGroundingChunks: [] };
    return;
  }

  try {
    const chat = getChatInstance();

    const request: SendMessageParameters = {
        message: messageText,
    };

     if (useWebSearch) {
        request.config = {
            ...(request.config || {}),
            tools: [{ googleSearch: {} }],
        };
    }

    const stream = await chat.sendMessageStream(request);
    let finalGroundingChunks: GroundingChunk[] | undefined;

    for await (const chunk of stream) {
      finalGroundingChunks = extractGroundingChunks(chunk.candidates?.[0]);
      yield { textChunk: chunk.text, finalGroundingChunks }; // Yield grounding chunks with each text chunk if available
    }
     // It's possible that groundingMetadata comes only in the last chunk or after all text.
     // The current structure yields it on each chunk if present. If it's only at the end,
     // this will correctly pass the last known version.

  } catch (error) {
    console.error("Error streaming message to Gemini:", error);
    if (error instanceof Error) {
        yield { error: error.message };
    } else {
        yield { error: "An unknown error occurred during streaming." };
    }
  }
}