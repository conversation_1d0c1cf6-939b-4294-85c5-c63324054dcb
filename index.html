
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gemini AI Chat</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      // Configure Tailwind CSS for class-based dark mode
      // This script must come AFTER the Tailwind CDN script
      tailwind.config = {
        darkMode: 'class',
        // You can add other theme customizations here if needed
        // theme: {
        //   extend: {
        //     colors: {
        //       // example
        //     }
        //   }
        // }
      };
    </script>
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react-markdown": "https://esm.sh/react-markdown@^10.1.0",
    "remark-gfm": "https://esm.sh/remark-gfm@^4.0.1",
    "@google/genai": "https://esm.sh/@google/genai@^1.6.0",
    "i18next": "https://esm.sh/i18next@^23.12.2",
    "react-i18next": "https://esm.sh/react-i18next@^15.0.0",
    "react-i": "https://esm.sh/react-i@^0.2.1"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body>
    <div id="root"></div>
    <script type="module" src="./index.tsx"></script>
  <script type="module" src="/index.tsx"></script>
</body>
</html>