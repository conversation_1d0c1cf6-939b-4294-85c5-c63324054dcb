
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// This function will be called from index.tsx to initialize i18next
export const initializeI18n = async () => {
  try {
    const [enResponse, ruResponse] = await Promise.all([
      fetch('./locales/en/translation.json'),
      fetch('./locales/ru/translation.json')
    ]);

    if (!enResponse.ok) {
      throw new Error(`Failed to fetch English translations: ${enResponse.status} ${enResponse.statusText}`);
    }
    if (!ruResponse.ok) {
      throw new Error(`Failed to fetch Russian translations: ${ruResponse.status} ${ruResponse.statusText}`);
    }

    const enTranslations = await enResponse.json();
    const ruTranslations = await ruResponse.json();

    console.log('[i18n] Loaded English translations via fetch:', enTranslations);
    console.log('[i18n] Loaded Russian translations via fetch:', ruTranslations);

    if (typeof enTranslations !== 'object' || enTranslations === null || Object.keys(enTranslations).length === 0) {
      throw new Error('[i18n] English translation data is missing, empty, or not a valid object after fetch.');
    }
    if (typeof ruTranslations !== 'object' || ruTranslations === null || Object.keys(ruTranslations).length === 0) {
      throw new Error('[i18n] Russian translation data is missing, empty, or not a valid object after fetch.');
    }

    await i18n
      .use(initReactI18next) // passes i18n down to react-i18next
      .init({
        resources: {
          en: {
            translation: enTranslations,
          },
          ru: {
            translation: ruTranslations,
          },
        },
        lng: 'en', // default language
        fallbackLng: 'en',
        interpolation: {
          escapeValue: false, // react already safes from xss
        },
        debug: true, // Enable i18next debug mode to see more logs in console
      });
    return i18n; // Return the initialized instance
  } catch (error) {
    console.error('Failed to initialize i18n with fetched translations:', error);
    // Attempt a fallback initialization with empty resources so the app doesn't completely break
    // BUT re-throw the error so index.tsx can handle it.
    try {
      await i18n.use(initReactI18next).init({
          resources: { 
              en: { translation: { errorLoading: "Error loading translations." } },
              ru: { translation: { errorLoading: "Ошибка загрузки переводов." } }
          },
          lng: 'en', fallbackLng: 'en', debug: true,
      });
    } catch (fallbackError) {
        console.error('Error during i18n fallback initialization:', fallbackError);
    }
    throw error; // Re-throw the original error to be caught by index.tsx
  }
};

// Export the i18n instance. It will be configured by the initializeI18n function.
export default i18n;